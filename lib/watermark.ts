import sharp from 'sharp';
import path from 'path';

/**
 * Helper function to get the logo buffer, either from filesystem or by fetching from URL
 * Optimized for both local development and serverless environments
 */
async function getLogoBuffer(): Promise<Buffer> {
  // Try local filesystem first (works in development)
  const logoPath = path.join(process.cwd(), 'public', 'images', 'static', 'logos', 'positive7-logo.png');

  try {
    const fs = await import('fs');
    const logoBuffer = await fs.promises.readFile(logoPath);
    console.log('[WATERMARK] Logo loaded from filesystem');
    return logoBuffer;
  } catch (err) {
    console.log('[WATERMARK] Could not load logo from filesystem, trying URL fetch');

    // Determine the appropriate URL based on environment
    let logoUrl;
    if (process.env.VERCEL_URL) {
      // We're on Vercel - use the deployment URL
      logoUrl = `https://${process.env.VERCEL_URL}/images/static/logos/positive7-logo.png`;
    } else if (process.env.NEXT_PUBLIC_SITE_URL) {
      // Custom domain is set
      logoUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/images/static/logos/positive7-logo.png`;
    } else {
      // Fallback to localhost for development
      logoUrl = 'http://localhost:3000/images/static/logos/positive7-logo.png';
    }

    console.log(`[WATERMARK] Fetching logo from: ${logoUrl}`);

    try {
      const response = await fetch(logoUrl, {
        // Add timeout and headers for better reliability
        signal: AbortSignal.timeout(10000), // 10 second timeout
        headers: {
          'User-Agent': 'Positive7-Tourism-Watermark/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch logo: HTTP ${response.status} ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const logoBuffer = Buffer.from(arrayBuffer);
      console.log(`[WATERMARK] Logo loaded from URL (${logoBuffer.length} bytes)`);
      return logoBuffer;
    } catch (fetchError) {
      console.error('[WATERMARK] Failed to fetch logo from URL:', fetchError);
      throw new Error(`Logo not available: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
    }
  }
}

/**
 * Add watermark to an image buffer
 * @param imageBuffer The original image buffer
 * @returns Promise<Buffer> The watermarked image buffer
 */
export async function addWatermarkToImage(imageBuffer: Buffer): Promise<Buffer> {
  try {
    console.log('[WATERMARK] Starting watermark process...');

    // Get logo buffer (serverless-compatible)
    let logoBuffer: Buffer;
    try {
      logoBuffer = await getLogoBuffer();
    } catch (logoError) {
      console.warn('[WATERMARK] Logo file not found, returning original image:', logoError);
      return imageBuffer;
    }
    
    // Get image metadata
    const image = sharp(imageBuffer);
    const metadata = await image.metadata();
    
    if (!metadata.width || !metadata.height) {
      throw new Error('Could not get image dimensions');
    }
    
    console.log('[WATERMARK] Image dimensions:', {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format
    });
    
    // Calculate watermark size (15% of image width)
    const watermarkSize = Math.floor(metadata.width * 0.15);

    // Prepare watermark
    const watermark = await sharp(logoBuffer)
      .resize(watermarkSize, watermarkSize, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png()
      .toBuffer();

    // Position watermark in top-right corner with padding
    const padding = Math.floor(watermarkSize * 0.2);
    const left = metadata.width - watermarkSize - padding;
    const top = padding;
    
    console.log('[WATERMARK] Watermark position:', {
      size: watermarkSize,
      left,
      top,
      padding
    });
    
    // Apply watermark
    const watermarkedBuffer = await image
      .composite([
        {
          input: watermark,
          left,
          top,
          blend: 'over'
        }
      ])
      .jpeg({ quality: 90 }) // Convert to JPEG for better compatibility
      .toBuffer();
    
    console.log('[WATERMARK] ✅ Watermark applied successfully');
    return watermarkedBuffer;
    
  } catch (error) {
    console.error('[WATERMARK] ❌ Error adding watermark:', error);
    // Return original image if watermarking fails
    return imageBuffer;
  }
}

/**
 * Add watermark to an image file (deprecated - use addWatermarkToImage with buffers instead)
 * @param inputPath Path to the input image file
 * @param outputPath Path to save the watermarked image
 * @returns Promise<void>
 */
export async function addWatermarkToFile(inputPath: string, outputPath: string): Promise<void> {
  try {
    console.log('[WATERMARK] Processing file:', inputPath);

    const fs = await import('fs');
    const imageBuffer = await fs.promises.readFile(inputPath);
    const watermarkedBuffer = await addWatermarkToImage(imageBuffer);

    await fs.promises.writeFile(outputPath, watermarkedBuffer);
    console.log('[WATERMARK] ✅ Watermarked file saved to:', outputPath);

  } catch (error) {
    console.error('[WATERMARK] ❌ Error processing file:', error);
    throw error;
  }
}

/**
 * Check if watermark logo exists (serverless-compatible)
 * @returns Promise<boolean>
 */
export async function isWatermarkAvailable(): Promise<boolean> {
  try {
    await getLogoBuffer();
    return true;
  } catch {
    return false;
  }
}

/**
 * Get watermark logo path
 * @returns string
 */
export function getWatermarkLogoPath(): string {
  return path.join(process.cwd(), 'public', 'images', 'positive7-logo.png');
}
