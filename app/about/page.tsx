import { Metadata } from 'next';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import CacheManager from '@/components/cache/CacheManager';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import AboutClient from '@/components/about/AboutClient';

export const metadata: Metadata = {
  title: 'About Us - Positive7 Educational Tours',
  description: 'Learn about Positive7\'s 15+ years of experience in educational tours and student travel. Discover our mission, values, and commitment to transformative learning experiences.',
  keywords: 'about positive7, educational tour company, student travel experts, experiential learning, adventure education, school trip specialists',
  openGraph: {
    title: 'About Positive7 - Transforming Education Through Travel',
    description: 'Since 2009, Positive7 has been pioneering experiential learning through carefully crafted educational tours.',
    images: ['/images/about-hero.jpg'],
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Positive7 - Educational Tour Experts',
    description: 'Discover how Positive7 transforms education through travel and experiential learning.',
    images: ['/images/about-hero.jpg']
  }
};

// Loading component for Suspense fallback
function AboutPageLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading About Page...</p>
      </div>
    </div>
  );
}

export default function AboutPage() {
  return (
    <PageErrorBoundary context="about-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <Suspense fallback={<AboutPageLoading />}>
            <AboutClient />
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
